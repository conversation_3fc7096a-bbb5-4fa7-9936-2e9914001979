# Xiang-Chat

"Xiang-Chat" is a Next.js-based chat application that integrates multiple AI language model providers including:
- Zhipu
- Groq
- OpenRouter
- Google

Key technical features:
- Built with Next.js 15 and TypeScript
- Uses Drizzle ORM with PostgreSQL for data storage
- Authentication handled by Clerk
- UI components from Shadcn UI and Tailwind CSS
- Vercel AI SDK for AI model integration

The app allows users to:
- Create and manage chat sessions
- Switch between different AI models
- View chat history
- See model reasoning when available
- Copy chat messages

TODO:
- [ ] Finish the Setting page
- [ ] Upload images
- [ ] Tiering Users, allowing for free models without Registration but limited usage