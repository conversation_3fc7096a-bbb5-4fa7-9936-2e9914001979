{"name": "xiang-chat", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --turbo", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache"}, "dependencies": {"@ai-sdk/deepseek": "^0.1.17", "@ai-sdk/google": "^1.2.3", "@ai-sdk/groq": "^1.2.1", "@ai-sdk/openai": "^1.1.9", "@ai-sdk/react": "^1.2.2", "@clerk/clerk-react": "^5.25.4", "@clerk/nextjs": "^6.12.11", "@neondatabase/serverless": "^0.10.4", "@openrouter/ai-sdk-provider": "^0.4.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@t3-oss/env-nextjs": "^0.10.1", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "ai": "^4.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-orm": "^0.43.1", "framer-motion": "^12.4.2", "geist": "^1.3.0", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-themes": "^0.4.4", "postgres": "^3.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.3", "react-markdown-github-renderers": "^1.0.3", "react-speech-recognition": "^4.0.0", "react-spinners": "^0.15.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "undici": "^7.4.0", "zhipu-ai-provider": "^0.1.1", "zod": "^3.24.1"}, "devDependencies": {"@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-speech-recognition": "^3.9.6", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "drizzle-kit": "^0.31.1", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "eslint-plugin-drizzle": "^0.2.3", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.3", "typescript": "^5.5.3", "typescript-eslint": "^8.23.0"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "pnpm@9.12.3"}